// ignore: depend_on_referenced_packages
import 'package:async/async.dart';
import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'word_use_case.g.dart';

class WordUseCase {
  final WordRepository _wordRepository;
  final AuthUseCase _authUseCase;
  CancelableOperation? _filteredWordsOperation;
  CancelableOperation? _searchOperation;

  WordUseCase(this._wordRepository, this._authUseCase);

  Future<WordEntity> getWordById(String wordId) async {
    final isGuest = await _authUseCase.isGuest();
    return await _wordRepository.getWordById(
      wordId: wordId,
      isGuest: isGuest,
    );
  }

  Future<List<WordEntity>> searchWords({
    required String languageId,
    required String query,
    List<Spiciness>? spiciness,
  }) async {
    return await _wordRepository.searchWords(
      languageId: languageId,
      query: query,
      spiciness: spiciness,
    );
  }

  Future<List<WordEntity>> getWords({
    required String languageId,
    List<Spiciness>? spiciness,
  }) async {
    return await _wordRepository.getWords(
      languageId: languageId,
      spiciness: spiciness,
    );
  }

  Future<HomeResponse> getHome() async {
    final isGuest = await _authUseCase.isGuest();
    return await _wordRepository.getHome(isGuest: isGuest);
  }

  Future<bool> toggleBookmark(String wordId, bool currentStatus) async {
    final isGuest = await _authUseCase.isGuest();

    if (isGuest) {
      throw CussMeException(Str.current.guestBookmarkError);
    }

    final user = await _authUseCase.getCurrentUser();
    if (user == null) {
      throw CussMeException(Str.current.userNotLoggedIn);
    }

    return await _wordRepository.toggleBookmark(
      wordId: wordId,
      profileId: user.id,
      currentStatus: currentStatus,
    );
  }

  Future<bool> submitWordSuggestion({
    required String wordId,
    required String suggestion,
    required Spiciness spiciness,
  }) async {
    final wordSuggestion = WordSuggestionRequest(
      wordId: wordId,
      suggestion: suggestion,
      spiciness: spiciness,
    );

    return await _wordRepository.submitWordSuggestion(
        suggestion: wordSuggestion);
  }

  Future<bool> submitWordReport({
    required String wordId,
    required ReportReason reason,
    String? detail,
  }) async {
    final wordReport = WordReportRequest(
      wordId: wordId,
      reason: reason.name,
      detail: detail,
    );

    return await _wordRepository.submitWordReport(report: wordReport);
  }

  Future<List<ListItem>> search({required String query}) async {
    await _searchOperation?.cancel();

    _searchOperation = CancelableOperation.fromFuture(
      _wordRepository.search(query: query).then((response) {
        final List<ListItem> groupedItems = [];
        if (response.words.isNotEmpty) {
          groupedItems.add(ListItem.headerItem(Str.current.words));
          groupedItems.addAll(
            response.words.map((word) => ListItem.wordItem(word)).toList(),
          );
        }
        if (response.languages.isNotEmpty) {
          groupedItems.add(ListItem.headerItem(Str.current.languages));
          groupedItems.addAll(
            response.languages
                .map((language) => ListItem.languageItem(language))
                .toList(),
          );
        }
        return groupedItems;
      }),
    );

    return await _searchOperation?.value;
  }

  Future<List<WordEntity>> _filterWords(
    String languageId,
    List<Spiciness> spiciness,
  ) async {
    if (languageId.isEmpty || spiciness.isEmpty) {
      ExceptionHandler.handleError(Str.current.somethingWentWrong);
    }

    final request = FilterWordsRequest(
      languageId: languageId,
      spiciness: spiciness,
    );

    final isAuthenticated = !(await _authUseCase.isGuest());

    return await _wordRepository.filterWords(
      request: request,
      isAuthenticated: isAuthenticated,
    );
  }

  Future<List<ListItem>> getGroupedFilteredWords({
    required String languageId,
    required List<Spiciness> spiciness,
  }) async {
    try {
      await _filteredWordsOperation?.cancel();

      _filteredWordsOperation = CancelableOperation.fromFuture(
        _filterWords(languageId, spiciness).then((filteredWords) {
          return _createGroupedItems(
            filteredWords,
            getHeaderKey: (word) => word.word.firstLetter,
          );
        }),
      );

      return await _filteredWordsOperation?.value;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  Future<List<WordEntity>> _getBookmarks() async {
    final isGuest = await _authUseCase.isGuest();
    if (isGuest) {
      throw CussMeException(Str.current.guestBookmarkError);
    }

    final user = await _authUseCase.getCurrentUser();
    if (user == null) {
      throw CussMeException(Str.current.userNotLoggedIn);
    }

    return await _wordRepository.getBookmarks(userId: user.id);
  }

  Future<List<ListItem>> getGroupedBookmarks() async {
    final bookmarks = await _getBookmarks();
    return _createGroupedItems(
      bookmarks,
      getHeaderKey: (word) => word.language.name,
    );
  }

  List<ListItem> _createGroupedItems(
    List<WordEntity> words, {
    required String Function(WordEntity) getHeaderKey,
  }) {
    final List<ListItem> items = [];
    String? currentGroup;
    int wordCount = 0;

    for (final word in words) {
      final groupValue = getHeaderKey(word);

      if (currentGroup != groupValue) {
        items.add(ListItem.headerItem(groupValue));
        currentGroup = groupValue;
      }

      items.add(ListItem.wordItem(word));
      wordCount++;

      if (wordCount % 5 == 0) {
        items.add(const ListItem.adItem());
      }
    }

    return items;
  }
}

@riverpod
WordUseCase wordUseCase(Ref ref) {
  final wordRepository = ref.read(wordRepositoryProvider);
  final authUseCase = ref.read(authUseCaseProvider);
  return WordUseCase(wordRepository, authUseCase);
}
