import 'package:cussme/domain/domain.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../localization/generated/l10n.dart';
import '../repositories/auth_repository.dart';

part 'auth_use_case.g.dart';

class AuthUseCase {
  final AuthRepository _authRepository;

  AuthUseCase(this._authRepository);

  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    await _authRepository.signInWithEmailPassword(
      email: email,
      password: password,
    );

    await _updateUserInSharedPreferences();
  }

  Future<void> signUpWithEmail({
    required String email,
    required String password,
    required String firstname,
    String? lastname,
  }) async {
    final supabaseUser = await _authRepository.signUpWithEmailPassword(
      email: email,
      password: password,
    );

    await _authRepository.upsertProfile(
      user: supabaseUser,
      firstName: firstname,
      lastName: lastname,
    );

    await _updateUserInSharedPreferences();
  }

  Future<bool> signInWithGoogle() async {
    final googleSignInAccount = await _authRepository.getGoogleSignInAccount();
    await _authRepository.signUpWithGoogle(googleSignInAccount);

    final profileExists = await _authRepository.checkIfProfileExists();
    if (!profileExists) await _authRepository.insertCurrentUserProfile();

    await _updateUserInSharedPreferences();
    return profileExists;
  }

  Future<bool> signInWithFacebook() async {
    final fbAccessToken = await _authRepository.getFacebookAccessToken();
    await _authRepository.signUpWithFacebook(fbAccessToken);

    final profileExists = await _authRepository.checkIfProfileExists();
    if (!profileExists) await _authRepository.insertCurrentUserProfile();

    await _updateUserInSharedPreferences();
    return profileExists;
  }

  Future<void> sendPasswordResetEmail(String? email) async {
    String? emailToUse = email;

    if (emailToUse == null) {
      final currentUser = await _authRepository.getUserFromSharedPreferences();
      emailToUse = currentUser?.email;

      if (emailToUse == null) {
        throw ExceptionHandler.handleError(Str.current.userNotLoggedIn);
      }
    }

    final emailExists = await _authRepository.checkIfEmailExists(emailToUse);

    if (!emailExists) {
      throw ExceptionHandler.handleError(Str.current.emailNotFound);
    }

    await _authRepository.sendPasswordResetEmail(emailToUse);
  }

  Future<void> updatePasswordAndSignOut(
    String newPassword,
    String? token,
  ) async {
    if (token != null) {
      await _authRepository.verifyRecoveryOTP(token);
    }

    _checkIfUserCanResetPassword();

    await _authRepository.updatePassword(newPassword);
    await _clearAndSignOut();
  }

  void _checkIfUserCanResetPassword() {
    final supabaseUser = _authRepository.getCurrentSupabaseUser();

    if (supabaseUser == null) {
      throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
    }

    final provider = supabaseUser.provider;
    if (!provider.isEmail) {
      throw ExceptionHandler.handleError(
          Str.current.providerPasswordResetError);
    }
  }

  bool canUserResetPassword() {
    try {
      _checkIfUserCanResetPassword();
      return true;
    } on CussMeException catch (_) {
      return false;
    }
  }

  Future<UserEntity> updateProfile(
    String firstName,
    String? lastName,
  ) async {
    await _authRepository.updateProfile(firstName, lastName);

    return await _updateUserInSharedPreferences();
  }

  Future<UserEntity> updateSpiciness(Spiciness spiciness) async {
    await _authRepository.updateSpiciness(spiciness);

    return await _updateUserInSharedPreferences();
  }

  Future<UserEntity> updateLanguages(List<LanguageEntity> languages) async {
    await _authRepository.updateLanguages(languages);

    return await _updateUserInSharedPreferences();
  }

  Future<List<LanguageEntity>> getAllLanguages() async {
    return await _authRepository.getAllLanguages();
  }

  Future<void> setGuest() async {
    await _authRepository.clearSharedPreferences();
    await _authRepository.setGuest();
  }

  Future<bool> isGuest() async {
    return await _authRepository.isGuest();
  }

  Future<bool> shouldNavigateHome() async {
    final isGuest = await _authRepository.isGuest();
    final currentUser = await _authRepository.getUserFromSharedPreferences();

    return currentUser != null || isGuest;
  }

  Future<void> signOut() async {
    await _clearAndSignOut();
  }

  Future<void> deleteUser() async {
    await _authRepository.deleteCurrentUser();
    await _clearAndSignOut();
  }

  Future<UserEntity?> getCurrentUser() async {
    return await _authRepository.getUserFromSharedPreferences();
  }

  Future<void> signOutBasedOnProvider() async {
    final provider = _authRepository.getCurrentSupabaseUser()!.provider;

    await _authRepository.signOutSupabase();

    if (provider.isGoogle) {
      await _authRepository.signOutGoogle();
    } else if (provider.isFacebook) {
      await _authRepository.signOutFacebook();
    }
  }

  Future<void> _clearAndSignOut() async {
    await signOutBasedOnProvider();
    await _authRepository.clearSharedPreferences();
  }

  Future<UserEntity> _updateUserInSharedPreferences(
      {UserEntity? userEntity}) async {
    userEntity ??= await _authRepository.getProfile();

    await _authRepository.saveUserToSharedPreferences(userEntity);
    await _authRepository.removeGuest();

    return userEntity;
  }

  Future<void> syncCurrentUserToSupabase() async {
    try {
      await _authRepository.syncCurrentUserToSupabase();
    } catch (_) {}
  }
}

@riverpod
AuthUseCase authUseCase(Ref ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return AuthUseCase(authRepository);
}
