import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'intro_state.freezed.dart';

@freezed
sealed class IntroState with _$IntroState {
  const IntroState._();

  const factory IntroState({
    @Default(null) UserEntity? user,
    @Default([]) List<LanguageEntity> availableLanguages,
    @Default([]) List<LanguageEntity> selectedLanguages,
    @Default(null) Spiciness? selectedSpiciness,
    @Default(false) bool isLoading,
    @Default(0) int currentPage,
    @Default(true) bool isPrimaryButtonEnabled,
    @Default(ScreenLoadingState.loading) ScreenLoadingState loadingState,
  }) = _IntroState;

  bool get isLanguagePage => currentPage == 0;
  bool get isSpicinessPage => currentPage == 1;

  IntroState toLanguagePage() => copyWith(currentPage: 0);
  IntroState toSpicinessPage() => copyWith(currentPage: 1);

  IntroState setSelectedLanguages() =>
      copyWith(selectedLanguages: List<LanguageEntity>.from(user!.languages));

  String get primaryButtonText =>
      isSpicinessPage ? Str.current.finish : Str.current.next;
  String get secondaryButtonText =>
      isSpicinessPage ? Str.current.previous : Str.current.skip;
}
