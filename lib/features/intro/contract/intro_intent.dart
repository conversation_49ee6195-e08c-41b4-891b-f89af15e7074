import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'intro_intent.freezed.dart';

@freezed
sealed class IntroIntent with _$IntroIntent {
  const factory IntroIntent.languageSelected(LanguageEntity language) =
      LanguageSelectedIntent;
  const factory IntroIntent.spicinessSelected(Spiciness spiciness) =
      SpicinessSelectedIntent;
  const factory IntroIntent.skipToHome() = SkipToHomeIntent;
  const factory IntroIntent.pageChanged(int pageIndex) = PageChangedIntent;
  const factory IntroIntent.primaryButtonClicked() = PrimaryButtonClickedIntent;
  const factory IntroIntent.secondaryButtonClicked() =
      SecondaryButtonClickedIntent;
  const factory IntroIntent.retry() = RetryIntent;
}
