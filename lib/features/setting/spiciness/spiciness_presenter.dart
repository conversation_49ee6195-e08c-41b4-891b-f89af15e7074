import 'dart:async';

import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/use_cases/auth_use_case.dart';
import 'contract/spiciness_contract.dart';

part 'spiciness_presenter.g.dart';

@riverpod
class SpicinessPresenter extends _$SpicinessPresenter {
  StreamController<SpicinessSideEffect> sideEffects =
      StreamController<SpicinessSideEffect>();

  late final AuthUseCase _authUseCase;

  @override
  SpicinessState build() {
    ref.onDispose(() => sideEffects.close());
    _authUseCase = ref.read(authUseCaseProvider);

    _loadUserSpiciness();

    return const SpicinessState();
  }

  Future<void> _loadUserSpiciness() async {
    try {
      final user = await _authUseCase.getCurrentUser();
      if (user != null) {
        final userSpiciness = user.spiciness;
        state = state.copyWith(
          defaultSpiciness: userSpiciness,
          selectedSpiciness: userSpiciness,
        );
      }
    } on CussMeException catch (e) {
      sideEffects.safeAdd(SpicinessSideEffect.showMessage(e.toString()));
    } finally {
      _updateApplyButtonState();
    }
  }

  void intentHandler(SpicinessIntent intent) {
    switch (intent) {
      case final SelectSpicinessIntent intent:
        state = state.copyWith(selectedSpiciness: intent.spiciness);
        _updateApplyButtonState();
        break;
      case ApplyChangesIntent _:
        _applyChanges();
        break;
      case NavigateBackIntent _:
        sideEffects.safeAdd(const SpicinessSideEffect.navigateBack());
        break;
    }
  }

  void _updateApplyButtonState() {
    state = state.copyWith(
      isApplyEnabled: state.selectedSpiciness != state.defaultSpiciness &&
          state.defaultSpiciness != null,
    );
  }

  void _applyChanges() async {
    if (!state.isApplyEnabled || state.selectedSpiciness == null) return;

    try {
      state = state.copyWith(isLoading: true);

      final user = await _authUseCase.updateSpiciness(state.selectedSpiciness!);

      state = state.copyWith(defaultSpiciness: user.spiciness);
      sideEffects.safeAdd(
        SpicinessSideEffect.showMessage(
            Str.current.accountSettingsChangesSaved),
      );
    } on CussMeException catch (e) {
      sideEffects.safeAdd(SpicinessSideEffect.showMessage(e.toString()));
    } finally {
      state = state.copyWith(isLoading: false);
      _updateApplyButtonState();
    }
  }
}

@riverpod
Stream<SpicinessSideEffect> spicinessSideEffects(Ref ref) {
  return ref.read(spicinessPresenterProvider.notifier).sideEffects.stream;
}
