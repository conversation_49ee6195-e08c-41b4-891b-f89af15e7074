import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../localization/generated/l10n.dart';
import 'contract/spiciness_contract.dart';
import 'spiciness_presenter.dart';

class SpicinessScreen extends ConsumerWidget {
  const SpicinessScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final SpicinessState state = ref.watch(spicinessPresenterProvider);
    final presenter = ref.read(spicinessPresenterProvider.notifier);

    return AdMobScaffold(
      body: Column(
        children: [
          CussMeHeader(
            title: Str.of(context).chooseSpiciness,
            titleIcon: Icons.local_fire_department,
            backButtonText: Str.of(context).preferencesTitle,
            onBackPressed: () => presenter.intentHandler(
              const SpicinessIntent.navigateBack(),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    SpicinessRadio(
                      selectedSpiciness: state.selectedSpiciness,
                      onSpicinessSelected: (spiciness) {
                        presenter.intentHandler(
                          SpicinessIntent.selectSpiciness(spiciness),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          ApplyButton(
            onPressed: () => presenter.intentHandler(
              const SpicinessIntent.applyChanges(),
            ),
            isEnabled: state.isApplyEnabled,
            isLoading: state.isLoading,
          ),
        ],
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(spicinessSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateBackSideEffect _:
            Navigator.pop(context);
            break;
          case final ShowMessageSideEffect se:
            Toast.show(context, se.message);
            break;
        }
      });
    });
  }
}
