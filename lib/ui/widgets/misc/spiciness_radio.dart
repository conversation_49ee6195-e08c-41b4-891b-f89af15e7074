import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';

class SpicinessRadio extends StatelessWidget {
  final Spiciness? selectedSpiciness;
  final void Function(Spiciness) onSpicinessSelected;

  const SpicinessRadio({
    super.key,
    required this.selectedSpiciness,
    required this.onSpicinessSelected,
  });

  @override
  Widget build(BuildContext context) {
    const spicinessValues = Spiciness.values;

    return Column(
      children: [
        ...spicinessValues
            .map((spiciness) {
              final widget = _buildSpicinessItem(spiciness);
              return spiciness == Spiciness.spicy
                  ? [widget, _buildDivider()]
                  : widget;
            })
            .expand((element) => element is List ? element : [element])
            // ignore: unnecessary_to_list_in_spreads
            .toList(),
      ],
    );
  }

  Widget _buildSpicinessItem(Spiciness spiciness) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, right: 4),
      child: Material(
        child: InkWell(
          onTap: () => onSpicinessSelected(spiciness),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Radio<Spiciness>(
                  value: spiciness,
                  groupValue: selectedSpiciness,
                  onChanged: (value) {
                    if (value != null) {
                      onSpicinessSelected(value);
                    }
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity:
                      const VisualDensity(horizontal: -4, vertical: -4),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextWithHighlight(
                    text:
                        '${spiciness.getTitle()} - ${spiciness.getDescription()}',
                    highlightedText: spiciness.getTitle(),
                    style: TextStyles.titleMedium.copyWith(
                      color: Palette.onSurface,
                    ),
                    highlightStyle: TextStyles.titleMedium.copyWith(
                      color: Palette.primary,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 22, vertical: 4),
      child: Divider(color: Palette.outlineVariant, height: 1),
    );
  }
}
