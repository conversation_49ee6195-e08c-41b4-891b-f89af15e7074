import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'list_item.freezed.dart';

@freezed
sealed class ListItem with _$ListItem {
  const factory ListItem.headerItem(String header) = ListItemHeader;
  const factory ListItem.wordItem(WordEntity word) = ListItemWord;
  const factory ListItem.languageItem(LanguageEntity language) =
      ListItemLanguage;
  const factory ListItem.adItem() = ListItemAd;
}
